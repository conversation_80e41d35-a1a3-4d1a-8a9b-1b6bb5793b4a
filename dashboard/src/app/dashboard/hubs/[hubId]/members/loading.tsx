import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, Crown } from "lucide-react";
import Link from "next/link";

import { Shield } from "lucide-react";

export default function MembersLoading() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/hubs">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <Skeleton className="h-8 w-48" />
        </div>
        <Skeleton className="h-10 w-32 sm:w-36" />
      </div>
      {/* Navigation placeholder */}
      <div className="h-16 bg-gray-800/50 rounded-lg animate-pulse" />
      {/* Owner Card Skeleton */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="border-b border-gray-800/50">
          <div className="flex items-center">
            <div className="h-6 w-6 rounded-full bg-yellow-900/30 flex items-center justify-center mr-2">
              <Crown className="h-3.5 w-3.5 text-yellow-400" />
            </div>
            <Skeleton className="h-6 w-24" />
          </div>
          <Skeleton className="h-4 w-64 mt-1" />
        </CardHeader>
        <CardContent className="pt-4">
          <div className="p-4 rounded-lg bg-gray-900/30 border border-gray-800/50">
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div>
                <Skeleton className="h-5 w-32 mb-1" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Moderators Card Skeleton */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="border-b border-gray-800/50">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <div className="flex items-center">
                <div className="h-6 w-6 rounded-full bg-purple-900/30 flex items-center justify-center mr-2">
                  <Shield className="h-3.5 w-3.5 text-purple-400" />
                </div>
                <Skeleton className="h-6 w-48" />
              </div>
              <Skeleton className="h-4 w-64 mt-1" />
            </div>
            <Skeleton className="h-10 w-32 sm:w-36" />
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex items-center justify-between p-4 rounded-lg bg-gray-900/30 border border-gray-800/50"
              >
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-5 w-32 mb-1" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
