"use client";

import type { BasicHubConnection } from "@/app/dashboard/hubs/[hubId]/connections/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { ConfirmationDialog, ConfirmationDialogPresets } from "./confirmation-dialog";
import { formatDistanceToNow } from "date-fns";
import {
  ArrowUp,
  Clock,
  Copy,
  Hash,
  Home,
  PauseCircle,
  PlayCircle,
  PlusCircle,
  Search,
  SortAsc,
  SortDesc,
  Trash,
  Users,
  ExternalLink,
  Settings,
  CheckSquare,
  Square,
  Filter,
  X,
  Wifi,
  WifiOff,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

interface ServerData {
  id: string;
  name: string | null;
  iconUrl: string | null;
}

interface ImprovedConnectionsListProps {
  connections: (BasicHubConnection & { server: ServerData | null })[];
  hubId: string;
  canManage: boolean;
  isLoading?: boolean;
  onConnectionRemoved?: (connectionId: string) => void;
}

type SortOption = "lastActive" | "serverName" | "status";
type SortDirection = "asc" | "desc";
type FilterOption = "all" | "connected" | "paused";

interface ConfirmationState {
  isOpen: boolean;
  type: 'disconnect' | 'remove' | 'pause' | 'resume' | 'bulk-disconnect' | 'bulk-remove';
  connectionId?: string;
  serverName?: string;
  selectedIds?: string[];
}

export function ImprovedConnectionsList({
  connections,
  hubId,
  canManage,
  isLoading = false,
  onConnectionRemoved,
}: ImprovedConnectionsListProps) {
  const { toast } = useToast();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOption, setSortOption] = useState<SortOption>("lastActive");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [filterOption, setFilterOption] = useState<FilterOption>("all");
  const [selectedConnections, setSelectedConnections] = useState<Set<string>>(new Set());
  const [filteredConnections, setFilteredConnections] = useState<ImprovedConnectionsListProps["connections"][0][]>([]);
  const [visibleCount, setVisibleCount] = useState(12);
  const [confirmationState, setConfirmationState] = useState<ConfirmationState>({
    isOpen: false,
    type: 'disconnect'
  });
  const [isProcessing, setIsProcessing] = useState(false);

  // Filter, sort, and search connections
  useEffect(() => {
    let result = [...connections];

    // Apply status filter
    if (filterOption !== "all") {
      result = result.filter((connection) => {
        if (filterOption === "connected") return connection.connected;
        if (filterOption === "paused") return !connection.connected;
        return true;
      });
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (connection) =>
          connection.server?.name?.toLowerCase().includes(query) ||
          connection.serverId.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0;

      switch (sortOption) {
        case "lastActive":
          comparison =
            new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime();
          break;
        case "serverName":
          comparison = (a.server?.name || "").localeCompare(
            b.server?.name || ""
          );
          break;
        case "status":
          comparison = Number(b.connected) - Number(a.connected);
          break;
      }

      return sortDirection === "asc" ? comparison * -1 : comparison;
    });

    setFilteredConnections(result);
    setVisibleCount(12);
  }, [connections, searchQuery, sortOption, sortDirection, filterOption]);

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const visibleIds = filteredConnections.slice(0, visibleCount).map(c => c.id);
      setSelectedConnections(new Set(visibleIds));
    } else {
      setSelectedConnections(new Set());
    }
  };

  const handleSelectConnection = (connectionId: string, checked: boolean) => {
    const newSelected = new Set(selectedConnections);
    if (checked) {
      newSelected.add(connectionId);
    } else {
      newSelected.delete(connectionId);
    }
    setSelectedConnections(newSelected);
  };

  // Action handlers
  const handleSingleAction = (type: ConfirmationState['type'], connectionId: string, serverName: string) => {
    setConfirmationState({
      isOpen: true,
      type,
      connectionId,
      serverName,
    });
  };

  const handleBulkAction = (type: 'bulk-disconnect' | 'bulk-remove') => {
    if (selectedConnections.size === 0) return;

    setConfirmationState({
      isOpen: true,
      type,
      selectedIds: Array.from(selectedConnections),
    });
  };

  const handleConfirmAction = async () => {
    setIsProcessing(true);
    try {
      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (confirmationState.type === 'remove' && confirmationState.connectionId) {
        onConnectionRemoved?.(confirmationState.connectionId);
      }

      toast({
        title: "Action completed",
        description: "The operation was completed successfully.",
      });

      setSelectedConnections(new Set());
    } catch (error) {
      toast({
        title: "Action failed",
        description: "The operation could not be completed.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setConfirmationState({ isOpen: false, type: 'disconnect' });
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast({
          description: `${label} copied to clipboard`,
        });
      },
      (err) => {
        console.error("Could not copy text: ", err);
        toast({
          variant: "destructive",
          description: "Failed to copy to clipboard",
        });
      }
    );
  };

  const toggleSortDirection = () => {
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const loadMore = () => {
    setVisibleCount(prev => Math.min(prev + 12, filteredConnections.length));
  };

  const scrollToTop = () => {
    const card = document.querySelector(".connections-card");
    if (card) {
      card.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Get confirmation dialog props
  const getConfirmationProps = () => {
    const { type, connectionId, serverName, selectedIds } = confirmationState;

    switch (type) {
      case 'disconnect':
        return ConfirmationDialogPresets.disconnectServer(serverName || '');
      case 'remove':
        return ConfirmationDialogPresets.removeConnection(serverName || '');
      case 'pause':
        return ConfirmationDialogPresets.pauseConnection(serverName || '');
      case 'resume':
        return ConfirmationDialogPresets.resumeConnection(serverName || '');
      case 'bulk-disconnect':
        return ConfirmationDialogPresets.bulkDisconnect(selectedIds?.length || 0);
      case 'bulk-remove':
        return ConfirmationDialogPresets.bulkRemove(selectedIds?.length || 0);
      default:
        return ConfirmationDialogPresets.disconnectServer('');
    }
  };

  const visibleConnections = filteredConnections.slice(0, visibleCount);
  const hasMore = visibleCount < filteredConnections.length;
  const connectedCount = connections.filter(c => c.connected).length;
  const pausedCount = connections.filter(c => !c.connected).length;

  if (isLoading) {
    return (
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm connections-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Server Connections</CardTitle>
              <CardDescription>Loading connections...</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4 rounded-lg border border-gray-800/50">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-8 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm connections-card">
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <CardTitle className="text-xl font-semibold text-white">
                Server Connections
              </CardTitle>
              <CardDescription className="text-gray-400">
                {connections.length} server{connections.length !== 1 ? "s" : ""} total
                {" • "}
                <span className="text-green-400">{connectedCount} active</span>
                {" • "}
                <span className="text-amber-400">{pausedCount} paused</span>
              </CardDescription>
            </div>

            {canManage && (
              <Button
                asChild
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
              >
                <Link href={`/dashboard/servers?hubId=${hubId}`}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Connect Server
                </Link>
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search servers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-700/50 text-white placeholder:text-gray-500 focus:border-gray-600"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-700"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Filter */}
            <Select value={filterOption} onValueChange={(value: FilterOption) => setFilterOption(value)}>
              <SelectTrigger className="w-full sm:w-40 bg-gray-800/50 border-gray-700/50 text-white">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-800">
                <SelectItem value="all">All Servers</SelectItem>
                <SelectItem value="connected">Active Only</SelectItem>
                <SelectItem value="paused">Paused Only</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortOption} onValueChange={(value: SortOption) => setSortOption(value)}>
              <SelectTrigger className="w-full sm:w-40 bg-gray-800/50 border-gray-700/50 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-800">
                <SelectItem value="lastActive">Last Active</SelectItem>
                <SelectItem value="serverName">Server Name</SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={toggleSortDirection}
              className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 hover:text-white"
            >
              {sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>
          </div>

          {/* Bulk Actions */}
          {canManage && selectedConnections.size > 0 && (
            <div className="flex items-center justify-between p-3 bg-indigo-500/10 border border-indigo-500/20 rounded-lg">
              <div className="flex items-center gap-3">
                <Checkbox
                  checked={selectedConnections.size === visibleConnections.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm text-indigo-300">
                  {selectedConnections.size} selected
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('bulk-disconnect')}
                  className="border-amber-600/50 text-amber-300 hover:bg-amber-600/10"
                >
                  <PauseCircle className="h-4 w-4 mr-1" />
                  Pause
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('bulk-remove')}
                  className="border-red-600/50 text-red-300 hover:bg-red-600/10"
                >
                  <Trash className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              </div>
            </div>
          )}

          {/* Connections List */}
          {connections.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800/50 flex items-center justify-center">
                <Home className="h-8 w-8 text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-white mb-2">No Connections</h3>
              <p className="text-gray-400 mb-6 max-w-sm mx-auto">
                This hub is not connected to any Discord servers yet. Connect your first server to start building your community.
              </p>
              {canManage && (
                <Button
                  asChild
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
                >
                  <Link href={`/dashboard/servers?hubId=${hubId}`}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Connect Your First Server
                  </Link>
                </Button>
              )}
            </div>
          ) : filteredConnections.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800/50 flex items-center justify-center">
                <Search className="h-8 w-8 text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-white mb-2">No Results Found</h3>
              <p className="text-gray-400 mb-4">
                No servers match your current search and filter criteria.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery("");
                  setFilterOption("all");
                }}
                className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 hover:text-white"
              >
                Clear Filters
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {visibleConnections.map((connection) => (
                <div
                  key={connection.id}
                  className="group relative flex items-center gap-4 p-4 rounded-xl border border-gray-800/50 bg-gray-800/20 hover:bg-gray-800/40 hover:border-gray-700/50 transition-all duration-200"
                >
                  {/* Selection Checkbox */}
                  {canManage && (
                    <Checkbox
                      checked={selectedConnections.has(connection.id)}
                      onCheckedChange={(checked) => handleSelectConnection(connection.id, checked as boolean)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    />
                  )}

                  {/* Server Icon */}
                  <div className="relative flex-shrink-0">
                    <div className="h-12 w-12 rounded-xl overflow-hidden border border-gray-700/50 bg-gray-700">
                      {connection.server?.iconUrl ? (
                        <Image
                          src={connection.server.iconUrl}
                          alt={connection.server.name || 'Server'}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Users className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Status Indicator */}
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-900 flex items-center justify-center">
                      {connection.connected ? (
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                      ) : (
                        <div className="w-2 h-2 bg-amber-500 rounded-full" />
                      )}
                    </div>
                  </div>

                  {/* Server Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-white truncate">
                        {connection.server?.name || 'Unknown Server'}
                      </h4>
                      <Badge
                        variant={connection.connected ? "default" : "secondary"}
                        className={
                          connection.connected
                            ? "bg-green-500/20 text-green-300 border-green-500/30"
                            : "bg-amber-500/20 text-amber-300 border-amber-500/30"
                        }
                      >
                        {connection.connected ? (
                          <>
                            <Wifi className="h-3 w-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <WifiOff className="h-3 w-3 mr-1" />
                            Paused
                          </>
                        )}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <Hash className="h-3 w-3" />
                        <span className="font-mono">{connection.channelId}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>
                          {formatDistanceToNow(new Date(connection.lastActive), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    {/* Quick Actions */}
                    <div className="flex items-center gap-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(connection.channelId, "Channel ID")}
                        className="h-11 w-11 p-0 hover:bg-gray-700/50 touch-manipulation"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="h-11 w-11 p-0 hover:bg-gray-700/50 touch-manipulation"
                      >
                        <Link href={`/dashboard/connections/${connection.id}`}>
                          <Settings className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>

                    {/* Management Actions */}
                    {canManage && (
                      <div className="flex items-center gap-1">
                        {connection.connected ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSingleAction('pause', connection.id, connection.server?.name || 'Unknown Server')}
                            className="h-11 px-3 border-amber-600/50 text-amber-300 hover:bg-amber-600/10 min-w-[44px] touch-manipulation"
                          >
                            <PauseCircle className="h-4 w-4 sm:mr-1" />
                            <span className="hidden sm:inline">Pause</span>
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSingleAction('resume', connection.id, connection.server?.name || 'Unknown Server')}
                            className="h-11 px-3 border-green-600/50 text-green-300 hover:bg-green-600/10 min-w-[44px] touch-manipulation"
                          >
                            <PlayCircle className="h-4 w-4 sm:mr-1" />
                            <span className="hidden sm:inline">Resume</span>
                          </Button>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSingleAction('remove', connection.id, connection.server?.name || 'Unknown Server')}
                          className="h-11 px-3 border-red-600/50 text-red-300 hover:bg-red-600/10 min-w-[44px] touch-manipulation"
                        >
                          <Trash className="h-4 w-4 sm:mr-1" />
                          <span className="hidden sm:inline">Remove</span>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Load More */}
          {hasMore && (
            <div className="flex flex-col items-center gap-4 pt-6">
              <Button
                variant="outline"
                onClick={loadMore}
                className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 hover:text-white"
              >
                Load More ({filteredConnections.length - visibleCount} remaining)
              </Button>

              {visibleCount > 12 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={scrollToTop}
                  className="text-gray-400 hover:text-white hover:bg-gray-800/50"
                >
                  <ArrowUp className="h-4 w-4 mr-2" />
                  Back to Top
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationState.isOpen}
        onClose={() => setConfirmationState({ isOpen: false, type: 'disconnect' })}
        onConfirm={handleConfirmAction}
        isLoading={isProcessing}
        {...getConfirmationProps()}
      />
    </>
  );
}
