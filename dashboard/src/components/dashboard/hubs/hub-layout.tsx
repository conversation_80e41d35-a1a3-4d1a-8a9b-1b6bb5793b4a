'use client';

import { UnifiedHubHeader } from './unified-hub-header';
import { HubNavigationTabs } from './hub-navigation-tabs';

interface HubLayoutProps {
  hub: {
    id: string;
    name: string;
    description: string;
    iconUrl: string;
    private: boolean;
    nsfw: boolean;
    connectionCount: number;
  };
  currentTab: string;
  canModerate?: boolean;
  canEdit?: boolean;
  showBackButton?: boolean;
  backHref?: string;
  headerActions?: React.ReactNode;
  children: React.ReactNode;
}

export function HubLayout({
  hub,
  currentTab,
  canModerate = false,
  canEdit = false,
  showBackButton = true,
  backHref = '/dashboard/hubs',
  headerActions,
  children,
}: HubLayoutProps) {
  return (
    <div className="space-y-8">
      {/* Unified Hub Header */}
      <UnifiedHubHeader
        hub={hub}
        showBackButton={showBackButton}
        backHref={backHref}
        actions={headerActions}
      />

      {/* Navigation Tabs */}
      <div className="sticky top-0 z-20 bg-gray-950/80 backdrop-blur-md border-b border-gray-800/50">
        <HubNavigationTabs
          hubId={hub.id}
          currentTab={currentTab}
          canModerate={canModerate}
          canEdit={canEdit}
        />
      </div>

      {/* Page Content */}
      <div className="space-y-6">{children}</div>
    </div>
  );
}
