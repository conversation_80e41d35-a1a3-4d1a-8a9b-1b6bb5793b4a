"use client";

import type { BasicHubConnection } from "@/app/dashboard/hubs/[hubId]/connections/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { ConfirmationDialog, ConfirmationDialogPresets } from "./confirmation-dialog";
import { formatDistanceToNow } from "date-fns";
import {
  ArrowUp,
  Clock,
  Copy,
  Hash,
  Home,
  MoreHorizontal,
  PauseCircle,
  PlayCircle,
  PlusCircle,
  Search,
  ShieldAlert,
  SortAsc,
  SortDesc,
  Trash,
  Users,
  ExternalLink,
  Settings,
  CheckSquare,
  Square,
  Filter,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

interface ServerData {
  id: string;
  name: string | null;
  iconUrl: string | null;
}

interface ConnectionsList {
  connections: (BasicHubConnection & { server: ServerData | null })[];
  hubId: string;
  canManage: boolean;
  isLoading?: boolean;
  onConnectionRemoved?: (connectionId: string) => void;
}

type SortOption = "lastActive" | "serverName" | "status";
type SortDirection = "asc" | "desc";
type FilterOption = "all" | "connected" | "paused";

interface ConfirmationState {
  isOpen: boolean;
  type: 'disconnect' | 'remove' | 'pause' | 'resume' | 'bulk-disconnect' | 'bulk-remove';
  connectionId?: string;
  serverName?: string;
  selectedIds?: string[];
}

export function ConnectionsList({
  connections,
  hubId,
  canManage,
  isLoading = false,
  onConnectionRemoved,
}: ConnectionsList) {
  const { toast } = useToast();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOption, setSortOption] = useState<SortOption>("lastActive");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [filterOption, setFilterOption] = useState<FilterOption>("all");
  const [selectedConnections, setSelectedConnections] = useState<Set<string>>(new Set());
  const [filteredConnections, setFilteredConnections] = useState<ConnectionsList["connections"][0][]>([]);
  const [visibleCount, setVisibleCount] = useState(12); // Show 12 connections initially
  const [confirmationState, setConfirmationState] = useState<ConfirmationState>({
    isOpen: false,
    type: 'disconnect'
  });
  const [isProcessing, setIsProcessing] = useState(false);

  // Function to scroll to top of the card
  const scrollToTop = () => {
    const card = document.querySelector(".connections-card");
    if (card) {
      card.scrollIntoView({ behavior: "smooth" });
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast({
          description: `${label} copied to clipboard`,
        });
      },
      (err) => {
        console.error("Could not copy text: ", err);
        toast({
          variant: "destructive",
          description: "Failed to copy to clipboard",
        });
      }
    );
  };


  // Filter, sort, and search connections
  useEffect(() => {
    let result = [...connections];

    // Apply status filter
    if (filterOption !== "all") {
      result = result.filter((connection) => {
        if (filterOption === "connected") return connection.connected;
        if (filterOption === "paused") return !connection.connected;
        return true;
      });
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (connection) =>
          connection.server?.name?.toLowerCase().includes(query) ||
          connection.serverId.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0;

      switch (sortOption) {
        case "lastActive":
          comparison =
            new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime();
          break;
        case "serverName":
          comparison = (a.server?.name || "").localeCompare(
            b.server?.name || ""
          );
          break;
        case "status":
          comparison = Number(b.connected) - Number(a.connected);
          break;
      }

      return sortDirection === "asc" ? comparison * -1 : comparison;
    });

    setFilteredConnections(result);
    setVisibleCount(12); // Reset visible count when filters change
  }, [connections, searchQuery, sortOption, sortDirection, filterOption]);

  const toggleSortDirection = () => {
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const getSortIcon = () => {
    return sortDirection === "asc" ? (
      <SortAsc className="h-4 w-4" />
    ) : (
      <SortDesc className="h-4 w-4" />
    );
  };

  const handleRemoveConnection = (connectionId: string) => {
    // Close the confirmation dialog
    setConnectionToRemove(null);

    // Call the callback if provided
    if (onConnectionRemoved) {
      onConnectionRemoved(connectionId);
    }

    // Update the filtered connections list immediately for better UX
    setFilteredConnections((prev) =>
      prev.filter((conn) => conn.id !== connectionId)
    );
  };

  if (isLoading) {
    return (
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>
            <Skeleton className="h-9 w-36" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-10 w-64" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-10" />
            </div>
          </div>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <ConnectionSkeleton key={index} />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm connections-card">
      <CardHeader>
        <CardTitle>Server Connections</CardTitle>
        <CardDescription>
          {connections.length} server{connections.length !== 1 ? "s" : ""}{" "}
          connected to this hub ({connections.filter((c) => c.connected).length}{" "}
          active, {connections.filter((c) => !c.connected).length} paused)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {connections.length === 0 ? (
          <div className="text-center py-8">
            <Home className="h-12 w-12 mx-auto text-gray-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">No Connections</h3>
            <p className="text-gray-400 mb-4">
              This hub is not connected to any Discord servers yet.
            </p>
            {canManage && (
              <Button
                asChild
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
              >
                <Link href={`/hubs/${hubId}`}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Connection
                </Link>
              </Button>
            )}
          </div>
        ) : (
          <>
            <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search connections..."
                  className="pl-8 bg-gray-900/50 border-gray-800/50 focus:ring-indigo-500/30 focus-visible:ring-indigo-500/30 rounded-md"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <Select
                  value={sortOption}
                  onValueChange={(value) => setSortOption(value as SortOption)}
                >
                  <SelectTrigger className="w-[180px] bg-gray-900/50 border-gray-800/50 focus:ring-indigo-500/30 focus-visible:ring-indigo-500/30 rounded-md">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50">
                    <SelectItem value="lastActive">Last Active</SelectItem>
                    <SelectItem value="serverName">Server Name</SelectItem>
                    <SelectItem value="messageCount">Message Count</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleSortDirection}
                  className="bg-gray-900/50 border-gray-800/50 hover:bg-gray-800/30 transition-colors duration-200 rounded-md"
                >
                  {getSortIcon()}
                </Button>
              </div>
            </div>

            {filteredConnections.length === 0 ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 mx-auto text-gray-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  No Matching Connections
                </h3>
                <p className="text-gray-400 mb-4">
                  No connections match your search criteria.
                </p>
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  Clear Search
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredConnections
                  .slice(0, visibleCount)
                  .map((connection) => (
                    <div
                      key={connection.id}
                      className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-md bg-gray-900/50 border border-gray-800/50 gap-4 hover:bg-gray-800/30 transition-colors duration-200"
                    >
                      <div className="flex items-center gap-3">
                        <div className="relative flex-shrink-0">
                          <div className="h-10 w-10 rounded-md bg-gray-800/80 border border-gray-700/50 flex items-center justify-center overflow-hidden">
                            {connection.server?.name ? (
                              <Image
                                src={connection.server.iconUrl||`https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
                                  connection.serverId
                                )}`}
                                alt={connection.server.name}
                                width={40}
                                height={40}
                                unoptimized
                                className="rounded-md object-cover"
                                style={{ width: "100%", height: "100%" }}
                                onError={(e) => {
                                  e.currentTarget.src =
                                    `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
                                    connection.serverId
                                  )}`
                                }}
                              />
                            ) : (
                              <Home className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                          <div className="absolute -bottom-1 -right-1 bg-gray-900/80 rounded-full p-0.5 border border-gray-700/50">
                            <Hash className="h-3.5 w-3.5 text-blue-400" />
                          </div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <div className="font-medium truncate">
                              {connection.server?.name || "Unknown Server"}
                            </div>
                            {connection.connected ? (
                              <span className="px-1.5 py-0.5 text-xs rounded-full bg-green-500/20 text-green-400 border border-green-500/30 flex items-center gap-1">
                                <PlayCircle className="h-3 w-3" />
                                Active
                              </span>
                            ) : (
                              <span className="px-1.5 py-0.5 text-xs rounded-full bg-amber-500/20 text-amber-400 border border-amber-500/30 flex items-center gap-1">
                                <PauseCircle className="h-3 w-3" />
                                Paused
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-400 flex items-center gap-2">
                            <span className="truncate">
                              Channel ID: {connection.channelId}
                            </span>
                            <button
                              onClick={() =>
                                copyToClipboard(
                                  connection.channelId,
                                  "Channel ID"
                                )
                              }
                              className="text-gray-500 hover:text-gray-300 flex-shrink-0"
                            >
                              <Copy className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="hidden md:flex flex-col items-end">
                          <div className="text-xs text-gray-400 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            Last active{" "}
                            {formatDistanceToNow(
                              new Date(connection.lastActive),
                              { addSuffix: true }
                            )}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="end"
                            className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50"
                          >
                            <DropdownMenuItem
                              onClick={() =>
                                copyToClipboard(
                                  connection.serverId,
                                  "Server ID"
                                )
                              }
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Copy Server ID
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                copyToClipboard(
                                  connection.channelId,
                                  "Channel ID"
                                )
                              }
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Copy Channel ID
                            </DropdownMenuItem>
                            {canManage && (
                              <>
                                <DropdownMenuItem asChild>
                                  <Link
                                    href={`/dashboard/moderation/blacklist/add?serverId=${connection.serverId}`}
                                  >
                                    <ShieldAlert className="h-4 w-4 mr-2 text-orange-400" />
                                    Blacklist Server
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className="text-red-500 focus:text-red-500"
                                  onClick={() =>
                                    setConnectionToRemove(connection.id)
                                  }
                                >
                                  <Trash className="h-4 w-4 mr-2" />
                                  Remove Connection
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}

                {/* Load More Button */}
                {visibleCount < filteredConnections.length ? (
                  <div className="text-center py-4 mt-2">
                    <div className="flex flex-col sm:flex-row gap-2 justify-center">
                      <Button
                        variant="outline"
                        onClick={() => setVisibleCount((prev) => prev + 15)}
                        className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                      >
                        Load More ({visibleCount} of{" "}
                        {filteredConnections.length})
                      </Button>
                      {filteredConnections.length > visibleCount + 15 && (
                        <Button
                          variant="outline"
                          onClick={() => {
                            setVisibleCount(filteredConnections.length);
                            // Add a small delay before scrolling to ensure the DOM has updated
                            setTimeout(scrollToTop, 100);
                          }}
                          className="border-indigo-700/50 bg-indigo-900/20 hover:bg-indigo-800/30 text-indigo-300 hover:text-indigo-200"
                        >
                          Show All
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  connections.length > 0 && (
                    <div className="text-center py-2 mt-4">
                      <p className="text-sm text-gray-400 mb-2">
                        Showing all {filteredConnections.length} connection
                        {filteredConnections.length !== 1 ? "s" : ""}
                      </p>
                      {visibleCount > 15 && filteredConnections.length > 15 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={scrollToTop}
                          className="mt-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                        >
                          <ArrowUp className="h-3.5 w-3.5 mr-2" />
                          Back to Top
                        </Button>
                      )}
                    </div>
                  )
                )}
              </div>
            )}
          </>
        )}

        {/* Confirmation Dialog for Removing Connection */}
        {connectionToRemove && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 border border-gray-800/50 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
              <h3 className="text-lg font-medium mb-2">Remove Connection</h3>
              <p className="text-gray-400 mb-4">
                Are you sure you want to remove this connection? This action
                cannot be undone.
              </p>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setConnectionToRemove(null)}
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleRemoveConnection(connectionToRemove)}
                  className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 border-none"
                >
                  Remove
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ConnectionSkeleton() {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-md bg-gray-900/50 border border-gray-800/50 gap-4">
      <div className="flex items-center gap-3">
        <div className="relative flex-shrink-0">
          <Skeleton className="h-10 w-10 rounded-md" />
          <div className="absolute -bottom-1 -right-1 bg-gray-900/80 rounded-full p-0.5 border border-gray-700/50">
            <Skeleton className="h-3.5 w-3.5 rounded-full" />
          </div>
        </div>
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-2 mb-1">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-16 rounded-full" />
          </div>
          <Skeleton className="h-3 w-48" />
        </div>
      </div>
      <div className="flex items-center gap-4">
        <div className="hidden md:flex flex-col items-end">
          <Skeleton className="h-3 w-24 mb-1" />
        </div>
        <Skeleton className="h-8 w-8 rounded-md" />
      </div>
    </div>
  );
}
